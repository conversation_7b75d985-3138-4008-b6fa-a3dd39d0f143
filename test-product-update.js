// Simple test to verify the product update mapping works
// This can be run to test the fix

const testUpdateProductMapping = () => {
  console.log('Testing UpdateProductDto mapping...');
  
  // Simulate the updateProductDto object
  const updateProductDto = {
    name: 'Updated Product Name',
    description: 'Updated Product Description'
  };
  
  // Simulate the existing product object
  const product = {
    id: 1,
    name: 'Original Product Name',
    description: 'Original Product Description',
    status: 'A',
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'SYSTEM',
    updatedBy: 'SYSTEM'
  };
  
  console.log('Before mapping:');
  console.log('UpdateProductDto:', updateProductDto);
  console.log('Product:', product);
  
  // This simulates what the AutoMapper mutateAsync should do
  // After the fix, the AutoMapper should properly map these properties
  Object.keys(updateProductDto).forEach(key => {
    if (updateProductDto[key] !== undefined) {
      product[key] = updateProductDto[key];
    }
  });
  
  console.log('After mapping (expected result):');
  console.log('Product:', product);
  
  return product;
};

// Run the test
testUpdateProductMapping();
